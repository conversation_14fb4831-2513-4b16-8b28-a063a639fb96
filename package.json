{"name": "tibco-bw-to-springboot-cli", "version": "1.0.0", "description": "CLI tool to convert TIBCO BW XSD schemas to Java Spring Boot classes", "main": "dist/cli.js", "bin": {"tibco-bw-to-springboot": "dist/cli.js"}, "scripts": {"build": "tsc", "start": "node dist/cli/index.js", "dev": "ts-node src/cli/index.ts", "test": "jest", "test:watch": "jest --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts"}, "keywords": ["tibco", "bw", "springboot", "xsd", "java", "cli"], "author": "", "license": "MIT", "dependencies": {"@types/glob": "^9.0.0", "chalk": "^4.1.2", "commander": "^11.1.0", "glob": "^11.0.3", "inquirer": "^8.2.6", "ora": "^5.4.1", "xml2js": "^0.6.2"}, "devDependencies": {"@types/inquirer": "^8.2.10", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}}