package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Root Element
 */
@JsonRootName("HttpHeaders")
@JsonIgnoreProperties(ignoreUnknown = true)
public class HttpHeaders {

    @JsonProperty("accept")
    private String accept;

    @JsonProperty("acceptCharset")
    private String acceptCharset;

    @JsonProperty("acceptEncoding")
    private String acceptEncoding;

    @JsonProperty("contentType")
    private String contentType;

    @JsonProperty("contentLength")
    private String contentLength;

    @JsonProperty("connection")
    private String connection;

    @JsonProperty("cookie")
    private String cookie;

    @JsonProperty("pragma")
    private String pragma;

    @JsonProperty("authorization")
    private String authorization;

    @Valid
    @JsonProperty("dynamicHeaders")
    private DynamicHeadersType dynamicHeaders;

    /**
     * Default constructor
     */
    public HttpHeaders() {
    }

    /**
     * Parameterized constructor
     */
    public HttpHeaders(String accept, String acceptCharset, String acceptEncoding, String contentType, String contentLength, String connection, String cookie, String pragma, String authorization, DynamicHeadersType dynamicHeaders) {
        this.accept = accept;
        this.acceptCharset = acceptCharset;
        this.acceptEncoding = acceptEncoding;
        this.contentType = contentType;
        this.contentLength = contentLength;
        this.connection = connection;
        this.cookie = cookie;
        this.pragma = pragma;
        this.authorization = authorization;
        this.dynamicHeaders = dynamicHeaders;
    }

    /**
     * Get accept
     */
    public String getAccept() {
        return accept;
    }

    /**
     * Set accept
     */
    public void setAccept(String accept) {
        this.accept = accept;
    }


    /**
     * Get acceptCharset
     */
    public String getAcceptCharset() {
        return acceptCharset;
    }

    /**
     * Set acceptCharset
     */
    public void setAcceptCharset(String acceptCharset) {
        this.acceptCharset = acceptCharset;
    }


    /**
     * Get acceptEncoding
     */
    public String getAcceptEncoding() {
        return acceptEncoding;
    }

    /**
     * Set acceptEncoding
     */
    public void setAcceptEncoding(String acceptEncoding) {
        this.acceptEncoding = acceptEncoding;
    }


    /**
     * Get contentType
     */
    public String getContentType() {
        return contentType;
    }

    /**
     * Set contentType
     */
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }


    /**
     * Get contentLength
     */
    public String getContentLength() {
        return contentLength;
    }

    /**
     * Set contentLength
     */
    public void setContentLength(String contentLength) {
        this.contentLength = contentLength;
    }


    /**
     * Get connection
     */
    public String getConnection() {
        return connection;
    }

    /**
     * Set connection
     */
    public void setConnection(String connection) {
        this.connection = connection;
    }


    /**
     * Get cookie
     */
    public String getCookie() {
        return cookie;
    }

    /**
     * Set cookie
     */
    public void setCookie(String cookie) {
        this.cookie = cookie;
    }


    /**
     * Get pragma
     */
    public String getPragma() {
        return pragma;
    }

    /**
     * Set pragma
     */
    public void setPragma(String pragma) {
        this.pragma = pragma;
    }


    /**
     * Get authorization
     */
    public String getAuthorization() {
        return authorization;
    }

    /**
     * Set authorization
     */
    public void setAuthorization(String authorization) {
        this.authorization = authorization;
    }


    /**
     * Get dynamicHeaders
     */
    public DynamicHeadersType getDynamicHeaders() {
        return dynamicHeaders;
    }

    /**
     * Set dynamicHeaders
     */
    public void setDynamicHeaders(DynamicHeadersType dynamicHeaders) {
        this.dynamicHeaders = dynamicHeaders;
    }

    @Override
    public String toString() {
        return "HttpHeaders{" +
                "accept=" + accept + ", " + "acceptCharset=" + acceptCharset + ", " + "acceptEncoding=" + acceptEncoding + ", " + "contentType=" + contentType + ", " + "contentLength=" + contentLength + ", " + "connection=" + connection + ", " + "cookie=" + cookie + ", " + "pragma=" + pragma + ", " + "authorization=" + authorization + ", " + "dynamicHeaders=" + dynamicHeaders +
                "}";
    }
}
