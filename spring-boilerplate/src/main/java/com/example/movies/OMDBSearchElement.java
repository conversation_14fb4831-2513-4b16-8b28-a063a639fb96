package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Generated from XSD schema
 * Namespace: /T1563811039923Converted/JsonSchema
 * Root Element
 */
@JsonRootName("OMDBSearchElement")
@JsonIgnoreProperties(ignoreUnknown = true)
public class OMDBSearchElement {

    @Valid
    @JsonProperty("search")
    private List<SearchType> search = new ArrayList<>();

    @JsonProperty("totalResults")
    private String totalResults;

    @JsonProperty("response")
    private String response;

    /**
     * Default constructor
     */
    public OMDBSearchElement() {
    }

    /**
     * Parameterized constructor
     */
    public OMDBSearchElement(List<SearchType> search, String totalResults, String response) {
        this.search = search;
        this.totalResults = totalResults;
        this.response = response;
    }

    /**
     * Get search
     */
    public List<SearchType> getSearch() {
        return search;
    }

    /**
     * Set search
     */
    public void setSearch(List<SearchType> search) {
        this.search = search;
    }


    /**
     * Get totalResults
     */
    public String getTotalResults() {
        return totalResults;
    }

    /**
     * Set totalResults
     */
    public void setTotalResults(String totalResults) {
        this.totalResults = totalResults;
    }


    /**
     * Get response
     */
    public String getResponse() {
        return response;
    }

    /**
     * Set response
     */
    public void setResponse(String response) {
        this.response = response;
    }

    @Override
    public String toString() {
        return "OMDBSearchElement{" +
                "search=" + search + ", " + "totalResults=" + totalResults + ", " + "response=" + response +
                "}";
    }
}
