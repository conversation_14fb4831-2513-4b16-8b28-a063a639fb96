package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Complex Type
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class JwtClaimElementType {

    @NotNull
    @NotBlank
    @JsonProperty("name")
    private String name;

    @NotNull
    @NotBlank
    @JsonProperty("value")
    private String value;

    /**
     * Default constructor
     */
    public JwtClaimElementType() {
    }

    /**
     * Parameterized constructor
     */
    public JwtClaimElementType(String name, String value) {
        this.name = name;
        this.value = value;
    }

    /**
     * Get name
     */
    public String getName() {
        return name;
    }

    /**
     * Set name
     */
    public void setName(String name) {
        this.name = name;
    }


    /**
     * Get value
     */
    public String getValue() {
        return value;
    }

    /**
     * Set value
     */
    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "JwtClaimElementType{" +
                "name=" + name + ", " + "value=" + value +
                "}";
    }
}
