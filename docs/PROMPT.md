# TIBCO Movie Example

## 步骤 1：Plan 实现 1 - 初始化和 Schemas 模型转换

我正在使用 JavaScript 实现一个 Tibco BW 转 Java + Spring Boot 的 CLI 工具。现在我，需要你实现如下的功能：

- 读取 Schemas 目录下的 .xsd 文件，转换为对应的 Java 类

要求：

1. 在解析完后，应该对比 Java 的 .xsd 实现的差异？
2. 我需要你认真概念设计 JavaScript 的类目录结构实现，方便未来迁移。
3. 需要编写对应的解析相关单元测试

## 步骤 2：Plan 逻辑实现 2 - 转换 Process 

### 2.1 继续 （Augment）

我正在使用 JavaScript 实现一个 Tibco BW 转 Java + Spring Boot 的 CLI 工具。现在我实现了基本的 xsd 解析，需要你实现 .bwp 解析，转换为对应的 Java 逻辑代码。

1. 解析 .bwp 文件，以 test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp 为例 
2. 转换逻辑到 Java 代码中，编写对应的单元测试
3. 接着应该复制到  spring-boilerplate 项目中，看能否启动项目

请确保所有的测试都是通过的

## 步骤 3. Plan 实现：Build and Fix（反复 N 轮）

我正在使用 JavaScript 实现一个 Tibco BW 转 Java + Spring Boot 的 CLI 工具。我实现了基本的 xsd 和 .bwp 解析，转换为对应的 Java 逻辑代码。
现在，请帮我通过 CLI 来实现：

1. 能把这个 searchMovie 的逻辑代码复制到正确的位置（可能是通过 Rule 或者文件名）
2. 能启动 spring-boilerplate 项目中，访问 API，以验证和 TIBCO BW 中的 swagger.json 是一致的接口
3. 如果可能的话，请编写测试

最后，请确保所有的测试都是通过的，

### toString 问题

我正在使用 JavaScript 实现一个 Tibco BW 转 Java + Spring Boot 的 CLI 工具。我实现了基本的 xsd 和 .bwp 解析，转换为对应的 Java 逻辑代码

现在，项目中的 toString 转换是有问题的。你需要：

1. 修复 toString 的转换问题
2. 把代码复制到 spring-boilerplate 中，看能否正确编译？

请确保所有的测试都是通过的

### 优化 CLI 自动化

优化 CLI 自动化流程。我正在使用 JavaScript 实现一个 Tibco BW 转 Java + Spring Boot 的 CLI 工具。如下是我已经实现的 CLI 命令：

```
node dist/cli.js convert \
  -i test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Processes/moviecatalogsearch/module/SearchMovies.bwp \
  -s test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Schemas \
  -o temp-output \
  -p com.example.movies \
  --spring-boot-project spring-boilerplate \
  --validate-api \
  --swagger-json test/_fixtures/MovieApi_Final_withConsul/MovieCatalogSearch.module/Resources/swagger.json
```

现在的问题是：

1. 需要优化 CLI 的自动化流程，确保转换后的代码能够正确运行。如果不行的话，需要实现代码，或者模板工程（spring-boilerplate）中，能够正确运行。
2. 简化现在的 CLI 命令，确保用户可以更容易地使用。

请确保所有的测试都是通过的。

### ++

优化 CLI 自动化流程。我正在使用 JavaScript 实现一个 Tibco BW 转 Java + Spring Boot 的 CLI 工具。
现在的问题是：

1. 需要优化 CLI 的自动化流程，确保转换后的代码能够正确运行。如果不行的话，需要实现代码，或者模板工程（spring-boilerplate）中，能够正确运行。
2. 简化 CLI 参数，去掉没有用的部分，按道理，我只需要输入一个 Tibco BW 的目录就能自动转换才对，诸如：test/_fixtures/

请确保所有的测试都是通过的。

## 步骤 4. 自动对比运行时结果

### Swagger 生成 API 测试代码？

我正在使用 JavaScript 实现一个 Tibco BW 转 Java + Spring Boot 的 CLI 工具。现在，请帮我创建一个新的 bin 命令，以：

1. 读取 Swagger 生成 API 测试代码；
2. 启动 Spring Boot 应用，使用步骤 1 生成的 API 测试代码，来校验是否正确
3. 尝试让这个过程更加流程

## 步骤 5. 项目测试，构建知识库

### 总结转换的 FAQ （多次）


## 步骤 5. 结合 AI 划分分层（可选）

### 生成 AI Agent



