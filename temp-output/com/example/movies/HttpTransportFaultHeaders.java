package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Complex Type
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class HttpTransportFaultHeaders {

    @JsonProperty("contentLength")
    private String contentLength;

    @JsonProperty("connection")
    private String connection;

    @JsonProperty("pragma")
    private String pragma;

    @JsonProperty("statusLine")
    private String statusLine;

    @JsonProperty("location")
    private String location;

    @JsonProperty("setCookie")
    private String setCookie;

    @JsonProperty("contentType")
    private String contentType;

    @Valid
    @JsonProperty("dynamicHeaders")
    private DynamicHeadersType dynamicHeaders;

    /**
     * Default constructor
     */
    public HttpTransportFaultHeaders() {
    }

    /**
     * Parameterized constructor
     */
    public HttpTransportFaultHeaders(String contentLength, String connection, String pragma, String statusLine, String location, String setCookie, String contentType, DynamicHeadersType dynamicHeaders) {
        this.contentLength = contentLength;
        this.connection = connection;
        this.pragma = pragma;
        this.statusLine = statusLine;
        this.location = location;
        this.setCookie = setCookie;
        this.contentType = contentType;
        this.dynamicHeaders = dynamicHeaders;
    }

    /**
     * Get contentLength
     */
    public String getContentLength() {
        return contentLength;
    }

    /**
     * Set contentLength
     */
    public void setContentLength(String contentLength) {
        this.contentLength = contentLength;
    }


    /**
     * Get connection
     */
    public String getConnection() {
        return connection;
    }

    /**
     * Set connection
     */
    public void setConnection(String connection) {
        this.connection = connection;
    }


    /**
     * Get pragma
     */
    public String getPragma() {
        return pragma;
    }

    /**
     * Set pragma
     */
    public void setPragma(String pragma) {
        this.pragma = pragma;
    }


    /**
     * Get statusLine
     */
    public String getStatusLine() {
        return statusLine;
    }

    /**
     * Set statusLine
     */
    public void setStatusLine(String statusLine) {
        this.statusLine = statusLine;
    }


    /**
     * Get location
     */
    public String getLocation() {
        return location;
    }

    /**
     * Set location
     */
    public void setLocation(String location) {
        this.location = location;
    }


    /**
     * Get setCookie
     */
    public String getSetCookie() {
        return setCookie;
    }

    /**
     * Set setCookie
     */
    public void setSetCookie(String setCookie) {
        this.setCookie = setCookie;
    }


    /**
     * Get contentType
     */
    public String getContentType() {
        return contentType;
    }

    /**
     * Set contentType
     */
    public void setContentType(String contentType) {
        this.contentType = contentType;
    }


    /**
     * Get dynamicHeaders
     */
    public DynamicHeadersType getDynamicHeaders() {
        return dynamicHeaders;
    }

    /**
     * Set dynamicHeaders
     */
    public void setDynamicHeaders(DynamicHeadersType dynamicHeaders) {
        this.dynamicHeaders = dynamicHeaders;
    }

    @Override
    public String toString() {
        return "HttpTransportFaultHeaders{" +
                "contentLength=" + contentLength + ", " + "connection=" + connection + ", " + "pragma=" + pragma + ", " + "statusLine=" + statusLine + ", " + "location=" + location + ", " + "setCookie=" + setCookie + ", " + "contentType=" + contentType + ", " + "dynamicHeaders=" + dynamicHeaders +
                "}";
    }
}
