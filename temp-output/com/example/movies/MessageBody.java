package com.example.movies;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/REST
 * Root Element
 */
@JsonRootName("MessageBody")
@JsonIgnoreProperties(ignoreUnknown = true)
public class MessageBody {



    /**
     * Default constructor
     */
    public MessageBody() {
    }



    @Override
    public String toString() {
        return "MessageBody{" +
                "}";
    }
}
