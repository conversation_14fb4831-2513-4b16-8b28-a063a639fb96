package com.example.movies.model;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://tns.tibco.com/bw/json/1563810299003
 * Complex Type
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class RatingsType {

    @JsonProperty("source")
    private String source;

    @JsonProperty("value")
    private String value;

    /**
     * Default constructor
     */
    public RatingsType() {
    }

    /**
     * Parameterized constructor
     */
    public RatingsType(String source, String value) {
        this.source = source;
        this.value = value;
    }

    /**
     * Get source
     */
    public String getSource() {
        return source;
    }

    /**
     * Set source
     */
    public void setSource(String source) {
        this.source = source;
    }


    /**
     * Get value
     */
    public String getValue() {
        return value;
    }

    /**
     * Set value
     */
    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "RatingsType{" +
                "source=" + source + ", " + "value=" + value +
                "}";
    }
}
