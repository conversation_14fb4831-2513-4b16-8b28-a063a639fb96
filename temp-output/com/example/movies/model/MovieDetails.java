package com.example.movies.model;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.util.ArrayList;
import java.util.List;

/**
 * Generated from XSD schema
 * Namespace: http://www.example.org/MovieCatalogMaster
 * Root Element
 */
@JsonRootName("MovieDetails")
@JsonIgnoreProperties(ignoreUnknown = true)
public class MovieDetails {

    @JsonProperty("title")
    private String title;

    @JsonProperty("year")
    private String year;

    @JsonProperty("rated")
    private String rated;

    @JsonProperty("released")
    private String released;

    @JsonProperty("runtime")
    private String runtime;

    @JsonProperty("genre")
    private String genre;

    @JsonProperty("director")
    private String director;

    @JsonProperty("writer")
    private String writer;

    @JsonProperty("actors")
    private String actors;

    @JsonProperty("plot")
    private String plot;

    @JsonProperty("language")
    private String language;

    @JsonProperty("country")
    private String country;

    @JsonProperty("awards")
    private String awards;

    @JsonProperty("poster")
    private String poster;

    @Valid
    @JsonProperty("ratings")
    private List<RatingsType> ratings = new ArrayList<>();

    @JsonProperty("metascore")
    private String metascore;

    @JsonProperty("imdbRating")
    private String imdbRating;

    @JsonProperty("imdbVotes")
    private String imdbVotes;

    @JsonProperty("imdbID")
    private String imdbID;

    @JsonProperty("type")
    private String type;

    @JsonProperty("dVD")
    private String dVD;

    @JsonProperty("boxOffice")
    private String boxOffice;

    @JsonProperty("production")
    private String production;

    @JsonProperty("website")
    private String website;

    @JsonProperty("response")
    private String response;

    /**
     * Default constructor
     */
    public MovieDetails() {
    }

    /**
     * Parameterized constructor
     */
    public MovieDetails(String title, String year, String rated, String released, String runtime, String genre, String director, String writer, String actors, String plot, String language, String country, String awards, String poster, List<RatingsType> ratings, String metascore, String imdbRating, String imdbVotes, String imdbID, String type, String dVD, String boxOffice, String production, String website, String response) {
        this.title = title;
        this.year = year;
        this.rated = rated;
        this.released = released;
        this.runtime = runtime;
        this.genre = genre;
        this.director = director;
        this.writer = writer;
        this.actors = actors;
        this.plot = plot;
        this.language = language;
        this.country = country;
        this.awards = awards;
        this.poster = poster;
        this.ratings = ratings;
        this.metascore = metascore;
        this.imdbRating = imdbRating;
        this.imdbVotes = imdbVotes;
        this.imdbID = imdbID;
        this.type = type;
        this.dVD = dVD;
        this.boxOffice = boxOffice;
        this.production = production;
        this.website = website;
        this.response = response;
    }

    /**
     * Get title
     */
    public String getTitle() {
        return title;
    }

    /**
     * Set title
     */
    public void setTitle(String title) {
        this.title = title;
    }


    /**
     * Get year
     */
    public String getYear() {
        return year;
    }

    /**
     * Set year
     */
    public void setYear(String year) {
        this.year = year;
    }


    /**
     * Get rated
     */
    public String getRated() {
        return rated;
    }

    /**
     * Set rated
     */
    public void setRated(String rated) {
        this.rated = rated;
    }


    /**
     * Get released
     */
    public String getReleased() {
        return released;
    }

    /**
     * Set released
     */
    public void setReleased(String released) {
        this.released = released;
    }


    /**
     * Get runtime
     */
    public String getRuntime() {
        return runtime;
    }

    /**
     * Set runtime
     */
    public void setRuntime(String runtime) {
        this.runtime = runtime;
    }


    /**
     * Get genre
     */
    public String getGenre() {
        return genre;
    }

    /**
     * Set genre
     */
    public void setGenre(String genre) {
        this.genre = genre;
    }


    /**
     * Get director
     */
    public String getDirector() {
        return director;
    }

    /**
     * Set director
     */
    public void setDirector(String director) {
        this.director = director;
    }


    /**
     * Get writer
     */
    public String getWriter() {
        return writer;
    }

    /**
     * Set writer
     */
    public void setWriter(String writer) {
        this.writer = writer;
    }


    /**
     * Get actors
     */
    public String getActors() {
        return actors;
    }

    /**
     * Set actors
     */
    public void setActors(String actors) {
        this.actors = actors;
    }


    /**
     * Get plot
     */
    public String getPlot() {
        return plot;
    }

    /**
     * Set plot
     */
    public void setPlot(String plot) {
        this.plot = plot;
    }


    /**
     * Get language
     */
    public String getLanguage() {
        return language;
    }

    /**
     * Set language
     */
    public void setLanguage(String language) {
        this.language = language;
    }


    /**
     * Get country
     */
    public String getCountry() {
        return country;
    }

    /**
     * Set country
     */
    public void setCountry(String country) {
        this.country = country;
    }


    /**
     * Get awards
     */
    public String getAwards() {
        return awards;
    }

    /**
     * Set awards
     */
    public void setAwards(String awards) {
        this.awards = awards;
    }


    /**
     * Get poster
     */
    public String getPoster() {
        return poster;
    }

    /**
     * Set poster
     */
    public void setPoster(String poster) {
        this.poster = poster;
    }


    /**
     * Get ratings
     */
    public List<RatingsType> getRatings() {
        return ratings;
    }

    /**
     * Set ratings
     */
    public void setRatings(List<RatingsType> ratings) {
        this.ratings = ratings;
    }


    /**
     * Get metascore
     */
    public String getMetascore() {
        return metascore;
    }

    /**
     * Set metascore
     */
    public void setMetascore(String metascore) {
        this.metascore = metascore;
    }


    /**
     * Get imdbRating
     */
    public String getImdbRating() {
        return imdbRating;
    }

    /**
     * Set imdbRating
     */
    public void setImdbRating(String imdbRating) {
        this.imdbRating = imdbRating;
    }


    /**
     * Get imdbVotes
     */
    public String getImdbVotes() {
        return imdbVotes;
    }

    /**
     * Set imdbVotes
     */
    public void setImdbVotes(String imdbVotes) {
        this.imdbVotes = imdbVotes;
    }


    /**
     * Get imdbID
     */
    public String getImdbID() {
        return imdbID;
    }

    /**
     * Set imdbID
     */
    public void setImdbID(String imdbID) {
        this.imdbID = imdbID;
    }


    /**
     * Get type
     */
    public String getType() {
        return type;
    }

    /**
     * Set type
     */
    public void setType(String type) {
        this.type = type;
    }


    /**
     * Get dVD
     */
    public String getDVD() {
        return dVD;
    }

    /**
     * Set dVD
     */
    public void setDVD(String dVD) {
        this.dVD = dVD;
    }


    /**
     * Get boxOffice
     */
    public String getBoxOffice() {
        return boxOffice;
    }

    /**
     * Set boxOffice
     */
    public void setBoxOffice(String boxOffice) {
        this.boxOffice = boxOffice;
    }


    /**
     * Get production
     */
    public String getProduction() {
        return production;
    }

    /**
     * Set production
     */
    public void setProduction(String production) {
        this.production = production;
    }


    /**
     * Get website
     */
    public String getWebsite() {
        return website;
    }

    /**
     * Set website
     */
    public void setWebsite(String website) {
        this.website = website;
    }


    /**
     * Get response
     */
    public String getResponse() {
        return response;
    }

    /**
     * Set response
     */
    public void setResponse(String response) {
        this.response = response;
    }

    @Override
    public String toString() {
        return "MovieDetails{" +
                "title=" + title + ", " + "year=" + year + ", " + "rated=" + rated + ", " + "released=" + released + ", " + "runtime=" + runtime + ", " + "genre=" + genre + ", " + "director=" + director + ", " + "writer=" + writer + ", " + "actors=" + actors + ", " + "plot=" + plot + ", " + "language=" + language + ", " + "country=" + country + ", " + "awards=" + awards + ", " + "poster=" + poster + ", " + "ratings=" + ratings + ", " + "metascore=" + metascore + ", " + "imdbRating=" + imdbRating + ", " + "imdbVotes=" + imdbVotes + ", " + "imdbID=" + imdbID + ", " + "type=" + type + ", " + "dVD=" + dVD + ", " + "boxOffice=" + boxOffice + ", " + "production=" + production + ", " + "website=" + website + ", " + "response=" + response +
                "}";
    }
}
