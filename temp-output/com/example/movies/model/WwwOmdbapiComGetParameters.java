package com.example.movies.model;
import com.fasterxml.jackson.annotation.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Generated from XSD schema
 * Namespace: http://xmlns.example.com/Default/parameters
 * Root Element
 */
@JsonRootName("WwwOmdbapiComGetParameters")
@JsonIgnoreProperties(ignoreUnknown = true)
public class WwwOmdbapiComGetParameters {

    @NotNull
    @NotBlank
    @JsonProperty("i")
    private String i;

    @NotNull
    @NotBlank
    @JsonProperty("apikey")
    private String apikey;

    /**
     * Default constructor
     */
    public WwwOmdbapiComGetParameters() {
    }

    /**
     * Parameterized constructor
     */
    public WwwOmdbapiComGetParameters(String i, String apikey) {
        this.i = i;
        this.apikey = apikey;
    }

    /**
     * Get i
     */
    public String getI() {
        return i;
    }

    /**
     * Set i
     */
    public void setI(String i) {
        this.i = i;
    }


    /**
     * Get apikey
     */
    public String getApikey() {
        return apikey;
    }

    /**
     * Set apikey
     */
    public void setApikey(String apikey) {
        this.apikey = apikey;
    }

    @Override
    public String toString() {
        return "WwwOmdbapiComGetParameters{" +
                "i=" + i + ", " + "apikey=" + apikey +
                "}";
    }
}
