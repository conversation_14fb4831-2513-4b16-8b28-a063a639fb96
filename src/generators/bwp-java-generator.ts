import {
  ParsedBWPProcess,
  ParsedRestEndpoint,
  JavaGenerationContext,
  JavaMethod,
  JavaParameter,
  JavaField,
  JavaGenerationOptions
} from '../types/index';

/**
 * BWP 到 Java Spring Boot 代码生成器
 */
export class BWPJavaGenerator {
  private options: JavaGenerationOptions;

  constructor(options: JavaGenerationOptions) {
    this.options = options;
  }

  /**
   * 生成 Spring Boot 控制器代码
   */
  generateController(process: ParsedBWPProcess): string {
    const context = this.createControllerContext(process);
    return this.generateJavaClass(context);
  }

  /**
   * 生成 Spring Boot 服务代码
   */
  generateService(process: ParsedBWPProcess): string {
    const context = this.createServiceContext(process);
    return this.generateJavaClass(context);
  }

  /**
   * 创建控制器生成上下文
   */
  private createControllerContext(process: ParsedBWPProcess): JavaGenerationContext {
    const className = `${process.name}Controller`;
    const serviceName = `${process.name}Service`;
    const serviceFieldName = this.toCamelCase(serviceName);

    const context: JavaGenerationContext = {
      packageName: this.options.packageName,
      className,
      imports: new Set([
        'org.springframework.web.bind.annotation.*',
        'org.springframework.beans.factory.annotation.Autowired',
        'org.springframework.http.ResponseEntity',
        'org.springframework.http.HttpStatus',
        'jakarta.validation.Valid',
        'jakarta.annotation.Nullable',
        'org.slf4j.Logger',
        'org.slf4j.LoggerFactory'
      ]),
      annotations: ['@RestController'],
      methods: [],
      fields: []
    };

    // 添加日志字段
    context.fields.push({
      name: 'logger',
      type: 'Logger',
      annotations: [],
      visibility: 'private static final',
      initialValue: `LoggerFactory.getLogger(${className}.class)`
    });

    // 添加服务字段
    context.fields.push({
      name: serviceFieldName,
      type: serviceName,
      annotations: ['@Autowired'],
      visibility: 'private'
    });

    // 为每个 REST 端点生成控制器方法
    for (const endpoint of process.restEndpoints) {
      const method = this.createControllerMethod(endpoint, serviceFieldName, process);
      context.methods.push(method);
    }

    // 添加必要的导入
    this.addTypeImports(context, process);

    return context;
  }

  /**
   * 创建服务生成上下文
   */
  private createServiceContext(process: ParsedBWPProcess): JavaGenerationContext {
    const className = `${process.name}Service`;

    const context: JavaGenerationContext = {
      packageName: this.options.packageName,
      className,
      imports: new Set([
        'org.springframework.stereotype.Service',
        'org.springframework.web.client.RestTemplate',
        'org.springframework.beans.factory.annotation.Autowired',
        'org.slf4j.Logger',
        'org.slf4j.LoggerFactory'
      ]),
      annotations: ['@Service'],
      methods: [],
      fields: []
    };

    // 添加 Logger 字段
    context.fields.push({
      name: 'logger',
      type: 'Logger',
      annotations: [],
      visibility: 'private',
      isStatic: true,
      isFinal: true
    });

    // 添加 RestTemplate 字段
    context.fields.push({
      name: 'restTemplate',
      type: 'RestTemplate',
      annotations: ['@Autowired'],
      visibility: 'private'
    });

    // 为每个 REST 端点生成服务方法
    for (const endpoint of process.restEndpoints) {
      const method = this.createServiceMethod(endpoint, process);
      context.methods.push(method);
    }

    // 添加必要的导入
    this.addTypeImports(context, process);

    return context;
  }

  /**
   * 创建控制器方法
   */
  private createControllerMethod(
    endpoint: ParsedRestEndpoint,
    serviceFieldName: string,
    process: ParsedBWPProcess
  ): JavaMethod {
    const methodName = this.toCamelCase(endpoint.operationName);
    const httpMethod = endpoint.method.toLowerCase();
    const mappingAnnotation = `@${this.capitalize(httpMethod)}Mapping("${endpoint.path}")`;

    const parameters: JavaParameter[] = [];
    const paramNames: string[] = [];

    // 添加路径参数和查询参数
    for (const param of endpoint.parameters) {
      const camelCaseName = this.toCamelCase(param.name);
      const javaType = this.mapDataTypeToJava(param.dataType);
      const paramAnnotation = param.parameterType === 'Query'
        ? `@RequestParam("${param.name}")${param.required ? '' : ' @Nullable'}`
        : `@PathVariable("${param.name}")`;

      parameters.push({
        name: camelCaseName,
        type: javaType,
        annotations: [paramAnnotation]
      });
      paramNames.push(camelCaseName);
    }

    // 添加输入类型参数
    if (process.interface.inputType && process.interface.inputType !== 'void') {
      const inputTypeName = this.capitalizeTypeName(this.extractTypeName(process.interface.inputType));

      if (httpMethod === 'post' || httpMethod === 'put') {
        // 对于 POST/PUT 请求，使用请求体
        parameters.push({
          name: 'request',
          type: inputTypeName,
          annotations: ['@RequestBody', '@Valid']
        });
        paramNames.push('request');
      }
      // 对于 GET/DELETE 请求，不添加额外的请求体参数，只使用路径和查询参数
    }

    const outputTypeName = this.capitalizeTypeName(this.extractTypeName(process.interface.outputType));
    const returnType = `ResponseEntity<${outputTypeName}>`;

    const serviceCall = `${serviceFieldName}.${methodName}(${paramNames.join(', ')})`;
    const body = `
        try {
            ${outputTypeName} result = ${serviceCall};
            return ResponseEntity.ok(result);
        } catch (IllegalArgumentException e) {
            return ResponseEntity.badRequest().build();
        } catch (Exception e) {
            logger.error("Error in ${methodName}: " + e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }`;

    return {
      name: methodName,
      returnType,
      parameters,
      annotations: [mappingAnnotation],
      body,
      visibility: 'public'
    };
  }

  /**
   * 创建服务方法
   */
  private createServiceMethod(
    endpoint: ParsedRestEndpoint,
    process: ParsedBWPProcess
  ): JavaMethod {
    const methodName = this.toCamelCase(endpoint.operationName);
    const outputTypeName = this.capitalizeTypeName(this.extractTypeName(process.interface.outputType));

    const parameters: JavaParameter[] = [];
    const paramNames: string[] = [];

    // 添加参数
    for (const param of endpoint.parameters) {
      const camelCaseName = this.toCamelCase(param.name);
      parameters.push({
        name: camelCaseName,
        type: this.mapDataTypeToJava(param.dataType),
        annotations: []
      });
      paramNames.push(camelCaseName);
    }

    // 如果有输入类型，添加请求参数（仅对 POST/PUT 请求）
    const httpMethod = endpoint.method.toLowerCase();
    if ((httpMethod === 'post' || httpMethod === 'put') &&
        process.interface.inputType && process.interface.inputType !== 'void') {
      const inputTypeName = this.capitalizeTypeName(this.extractTypeName(process.interface.inputType));
      parameters.push({
        name: 'request',
        type: inputTypeName,
        annotations: []
      });
      paramNames.push('request');
    }

    // 构建 REST 调用逻辑
    const restCallLogic = this.generateRestCallLogic(endpoint, paramNames, outputTypeName);

    const paramLog = paramNames.length > 0 ? paramNames.join(' + ", " + ') : '""';
    const body = `
        logger.info("Executing ${methodName} with parameters: {}", ${paramLog});

        try {
            ${restCallLogic}

            logger.info("Successfully completed ${methodName}");
            return result;
        } catch (Exception e) {
            logger.error("Error in ${methodName}: " + e.getMessage(), e);
            throw new RuntimeException("Service call failed", e);
        }`;

    return {
      name: methodName,
      returnType: outputTypeName,
      parameters,
      annotations: [],
      body,
      visibility: 'public'
    };
  }

  /**
   * 生成 REST 调用逻辑
   */
  private generateRestCallLogic(endpoint: ParsedRestEndpoint, paramNames: string[], outputTypeName: string): string {
    const method = endpoint.method.toUpperCase();

    if (method === 'GET') {
      const queryParams = endpoint.parameters
        .filter(p => p.parameterType === 'Query')
        .map(p => `"${p.name}=" + ${this.toCamelCase(p.name)}`)
        .join(' + "&" + ');

      const url = queryParams
        ? `"${endpoint.path}?" + ${queryParams}`
        : `"${endpoint.path}"`;

      return `${outputTypeName} result = restTemplate.getForObject(${url}, ${outputTypeName}.class);`;
    } else if (method === 'POST') {
      const requestBody = paramNames.find(name => name === 'request') || 'null';
      return `${outputTypeName} result = restTemplate.postForObject("${endpoint.path}", ${requestBody}, ${outputTypeName}.class);`;
    } else if (method === 'PUT') {
      const requestBody = paramNames.find(name => name === 'request') || 'null';
      return `restTemplate.put("${endpoint.path}", ${requestBody});
            ${outputTypeName} result = new ${outputTypeName}(); // TODO: Handle PUT response`;
    } else if (method === 'DELETE') {
      return `restTemplate.delete("${endpoint.path}");
            ${outputTypeName} result = new ${outputTypeName}(); // TODO: Handle DELETE response`;
    }

    return `${outputTypeName} result = new ${outputTypeName}(); // TODO: Implement ${method} call`;
  }

  /**
   * 生成 Java 类代码
   */
  private generateJavaClass(context: JavaGenerationContext): string {
    const imports = Array.from(context.imports).sort().map(imp => `import ${imp};`).join('\n');
    const annotations = context.annotations.join('\n');

    // 过滤掉静态 final logger 字段，因为我们会单独生成
    const nonLoggerFields = context.fields.filter(field =>
      !(field.name === 'logger' && field.isStatic && field.isFinal)
    );
    const fields = nonLoggerFields.map(field => this.generateField(field)).join('\n\n');
    const methods = context.methods.map(method => this.generateMethod(method)).join('\n\n');

    const loggerField = this.generateLoggerField(context);
    const fieldsSection = fields ? `\n    ${fields}` : '';
    const methodsSection = methods ? `\n    ${methods}` : '';

    return `package ${context.packageName};

${imports}

${annotations}
public class ${context.className} {${loggerField ? `\n    ${loggerField}` : ''}${fieldsSection}${methodsSection}
}`;
  }

  /**
   * 生成字段代码
   */
  private generateField(field: JavaField): string {
    const annotations = field.annotations.join('\n    ');
    const modifiers = [
      field.visibility,
      field.isStatic ? 'static' : '',
      field.isFinal ? 'final' : ''
    ].filter(Boolean).join(' ');

    const annotationPrefix = annotations ? `    ${annotations}\n` : '';
    const initialValue = field.initialValue ? ` = ${field.initialValue}` : '';

    return `${annotationPrefix}    ${modifiers} ${field.type} ${field.name}${initialValue};`;
  }

  /**
   * 生成方法代码
   */
  private generateMethod(method: JavaMethod): string {
    const annotations = method.annotations.map(ann => `    ${ann}`).join('\n');
    const parameters = method.parameters.map(param => {
      const paramAnnotations = param.annotations.join(' ');
      return `${paramAnnotations} ${param.type} ${param.name}`.trim();
    }).join(', ');

    const annotationPrefix = annotations ? `${annotations}\n` : '';
    
    return `${annotationPrefix}    ${method.visibility} ${method.returnType} ${method.name}(${parameters}) {${method.body}
    }`;
  }

  /**
   * 生成 Logger 字段
   */
  private generateLoggerField(context: JavaGenerationContext): string {
    if (context.fields.some(f => f.name === 'logger' && f.isStatic && f.isFinal)) {
      return `private static final Logger logger = LoggerFactory.getLogger(${context.className}.class);`;
    }
    return '';
  }

  /**
   * 添加类型导入
   */
  private addTypeImports(context: JavaGenerationContext, process: ParsedBWPProcess): void {
    // 暂时不添加命名空间导入，因为它们不是有效的 Java 包名
    // 在实际项目中，这些类型应该从 XSD 生成的 Java 类中导入

    // 添加常用的 Java 类型导入
    if (process.interface.inputType && process.interface.inputType !== 'void') {
      // context.imports.add(`com.example.model.${this.extractTypeName(process.interface.inputType)}`);
    }

    if (process.interface.outputType && process.interface.outputType !== 'void') {
      // context.imports.add(`com.example.model.${this.extractTypeName(process.interface.outputType)}`);
    }
  }

  /**
   * 工具方法：转换为驼峰命名
   */
  private toCamelCase(str: string): string {
    return str.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase())
              .replace(/^[A-Z]/, (match) => match.toLowerCase());
  }

  /**
   * 工具方法：首字母大写
   */
  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  /**
   * 工具方法：提取类型名称
   */
  private extractTypeName(fullType: string): string {
    const parts = fullType.split('.');
    return parts[parts.length - 1];
  }

  /**
   * 工具方法：将类型名称首字母大写
   */
  private capitalizeTypeName(typeName: string): string {
    return this.capitalize(typeName);
  }

  /**
   * 工具方法：映射数据类型到 Java 类型
   */
  private mapDataTypeToJava(dataType: string): string {
    const typeMap: { [key: string]: string } = {
      'string': 'String',
      'int': 'Integer',
      'integer': 'Integer',
      'long': 'Long',
      'double': 'Double',
      'float': 'Float',
      'boolean': 'Boolean',
      'date': 'LocalDate',
      'dateTime': 'LocalDateTime'
    };

    return typeMap[dataType.toLowerCase()] || 'String';
  }
}
