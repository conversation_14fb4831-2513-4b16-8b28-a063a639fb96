import * as xml2js from 'xml2js';
import {
  BWPProcess,
  ParsedBWPProcess,
  ParsedActivity,
  ParsedVariable,
  ParsedPartnerLink,
  ParsedRestEndpoint,
  ParsedRestBinding,
  ParsedRestOperation,
  ParsedRestOperationParameter
} from '../types/index';
import { readFileContent } from '../utils/file-utils';

/**
 * BWP (BPEL Process) 文件解析器
 */
export class BWPParser {
  private namespaceMap: Map<string, string> = new Map();

  /**
   * 解析 BWP 文件
   */
  async parseBWP(filePath: string): Promise<ParsedBWPProcess> {
    const content = readFileContent(filePath);
    const bwpProcess = await this.parseXMLToBWP(content);
    
    return this.convertToParseProcess(bwpProcess);
  }

  /**
   * 将 XML 字符串解析为 BWP 对象
   */
  private async parseXMLToBWP(content: string): Promise<BWPProcess> {
    return new Promise((resolve, reject) => {
      xml2js.parseString(content, {
        explicitArray: false,
        mergeAttrs: false,
        explicitRoot: true,
        tagNameProcessors: [xml2js.processors.stripPrefix]
      }, (err, result) => {
        if (err) {
          const errorMessage = err?.message || (typeof err === 'string' ? err : 'Unknown XML parsing error');
          reject(new Error(`Failed to parse BWP XML: ${errorMessage}`));
        } else {
          // 提取 process 节点 (可能是 process 或 bpws:process)
          const processNode = result.process || result['bpws:process'];
          if (!processNode) {
            reject(new Error('No process node found in BWP file'));
            return;
          }
          resolve(processNode as BWPProcess);
        }
      });
    });
  }

  /**
   * 转换为解析后的流程对象
   */
  private convertToParseProcess(bwpProcess: BWPProcess): ParsedBWPProcess {
    // 构建命名空间映射
    this.buildNamespaceMap(bwpProcess);

    const processInfo = this.parseProcessInfo(bwpProcess);
    const processInterface = this.parseProcessInterface(bwpProcess);
    const variables = this.parseVariables(bwpProcess);
    const partnerLinks = this.parsePartnerLinks(bwpProcess);
    const activities = this.parseActivities(bwpProcess);
    const restEndpoints = this.extractRestEndpoints(partnerLinks, processInterface);

    return {
      name: this.extractClassName(bwpProcess.$.name),
      namespace: bwpProcess.$.targetNamespace,
      processInfo,
      interface: processInterface,
      activities,
      variables,
      partnerLinks,
      restEndpoints
    };
  }

  /**
   * 构建命名空间映射
   */
  private buildNamespaceMap(bwpProcess: BWPProcess): void {
    this.namespaceMap.clear();

    if (bwpProcess.namespaceRegistry?.namespaceItem) {
      const items = Array.isArray(bwpProcess.namespaceRegistry.namespaceItem)
        ? bwpProcess.namespaceRegistry.namespaceItem
        : [bwpProcess.namespaceRegistry.namespaceItem];

      for (const item of items) {
        this.namespaceMap.set(item.$.prefix, item.$.namespace);
      }
    }
  }

  /**
   * 解析流程信息
   */
  private parseProcessInfo(bwpProcess: BWPProcess): ParsedBWPProcess['processInfo'] {
    // 检查 ProcessInfo (大写P)
    const processInfo = (bwpProcess as any).ProcessInfo || bwpProcess.processInfo;
    const info = processInfo?.$;

    return {
      callable: info?.callable === 'true',
      stateless: info?.stateless === 'true',
      type: info?.type || 'IT',
      modifiers: info?.modifiers || 'public'
    };
  }

  /**
   * 解析流程接口
   */
  private parseProcessInterface(bwpProcess: BWPProcess): ParsedBWPProcess['interface'] {
    // 检查 ProcessInterface (大写P)
    const processInterface = (bwpProcess as any).ProcessInterface || bwpProcess.processInterface;
    const iface = processInterface?.$;

    if (!iface) {
      throw new Error('No process interface found');
    }

    const inputInfo = this.parseTypeReference(iface.input);
    const outputInfo = this.parseTypeReference(iface.output);

    return {
      inputType: inputInfo.type,
      outputType: outputInfo.type,
      inputNamespace: inputInfo.namespace,
      outputNamespace: outputInfo.namespace
    };
  }

  /**
   * 解析类型引用
   */
  private parseTypeReference(typeRef?: string): { type: string; namespace?: string } {
    if (!typeRef) {
      return { type: 'void' };
    }

    // 格式1: {namespace}typeName
    const namespaceMatch = typeRef.match(/^\{([^}]+)\}(.+)$/);
    if (namespaceMatch) {
      return {
        namespace: namespaceMatch[1],
        type: namespaceMatch[2]
      };
    }

    // 格式2: prefix:typeName
    const prefixMatch = typeRef.match(/^([^:]+):(.+)$/);
    if (prefixMatch) {
      const prefix = prefixMatch[1];
      const typeName = prefixMatch[2];
      const namespace = this.namespaceMap.get(prefix);

      return {
        namespace: namespace,
        type: typeName
      };
    }

    return { type: typeRef };
  }

  /**
   * 解析变量
   */
  private parseVariables(bwpProcess: BWPProcess): ParsedVariable[] {
    const variables: ParsedVariable[] = [];

    if (bwpProcess.variables?.variable) {
      const variableList = Array.isArray(bwpProcess.variables.variable)
        ? bwpProcess.variables.variable
        : [bwpProcess.variables.variable];

      for (const variable of variableList) {
        const attrs = variable.$;
        const typeInfo = this.parseTypeReference(attrs.element || attrs.messageType);

        variables.push({
          name: attrs.name,
          type: attrs.element ? 'element' : 'messageType',
          dataType: typeInfo.type,
          namespace: typeInfo.namespace,
          isInternal: attrs['sca-bpel:internal'] === 'true',
          parameterType: attrs['tibex:parameter'] as 'in' | 'out' | undefined
        });
      }
    }

    return variables;
  }

  /**
   * 解析合作伙伴链接
   */
  private parsePartnerLinks(bwpProcess: BWPProcess): ParsedPartnerLink[] {
    const partnerLinks: ParsedPartnerLink[] = [];

    if (bwpProcess.partnerLinks?.partnerLink) {
      const partnerLinkList = Array.isArray(bwpProcess.partnerLinks.partnerLink)
        ? bwpProcess.partnerLinks.partnerLink
        : [bwpProcess.partnerLinks.partnerLink];

      for (const link of partnerLinkList) {
        const attrs = link.$;
        const restBinding = this.parseRestBinding(link);

        partnerLinks.push({
          name: attrs.name,
          partnerLinkType: attrs.partnerLinkType,
          role: attrs.partnerRole || 'use',
          restBinding
        });
      }
    }

    return partnerLinks;
  }

  /**
   * 解析 REST 绑定
   */
  private parseRestBinding(partnerLink: any): ParsedRestBinding | undefined {
    try {
      // 检查 ReferenceBinding (大写R)
      const refBinding = partnerLink.ReferenceBinding || partnerLink.referenceBinding;
      const binding = refBinding?.binding;
      const bwBaseBinding = binding?.BWBaseBinding;
      const referenceBinding = bwBaseBinding?.referenceBinding;
      const restBinding = referenceBinding?.binding;

      if (!restBinding || restBinding.$['xsi:type'] !== 'rest:RestReferenceBinding') {
        return undefined;
      }

      const operations: ParsedRestOperation[] = [];
      if (restBinding.operation) {
        const operationsArray = Array.isArray(restBinding.operation) ? restBinding.operation : [restBinding.operation];
        for (const op of operationsArray) {
          const params: ParsedRestOperationParameter[] = [];

          if (op.parameters?.parameterMapping) {
            const paramMappings = Array.isArray(op.parameters.parameterMapping)
              ? op.parameters.parameterMapping
              : [op.parameters.parameterMapping];

            for (const param of paramMappings) {
              params.push({
                name: param.$.parameterName,
                dataType: param.$.dataType,
                parameterType: param.$.parameterType,
                required: param.$.required === 'true'
              });
            }
          }

          operations.push({
            name: op.$.operationName,
            httpMethod: op.$.httpMethod,
            parameters: params,
            clientFormat: typeof op.clientFormat === 'string' ? op.clientFormat : 'json'
          });
        }
      }

      return {
        basePath: restBinding.$.basePath || '/',
        path: restBinding.$.path || '/',
        connector: restBinding.$.connector || '',
        docBasePath: restBinding.$.docBasePath || '',
        operations
      };
    } catch (error) {
      console.warn('Failed to parse REST binding:', error);
      return undefined;
    }
  }

  /**
   * 解析活动
   */
  private parseActivities(bwpProcess: BWPProcess): ParsedActivity[] {
    const activities: ParsedActivity[] = [];

    const scope = bwpProcess.scope;
    const flow = scope?.flow;

    if (!flow) {
      return activities;
    }

    // 解析扩展活动
    if (flow.extensionActivity) {
      const extActivities = Array.isArray(flow.extensionActivity) ? flow.extensionActivity : [flow.extensionActivity];
      for (const extActivity of extActivities) {
        const activity = this.parseExtensionActivity(extActivity);
        if (activity) {
          activities.push(activity);
        }
      }
    }

    // 解析调用活动
    if (flow.invoke) {
      const invokeActivities = Array.isArray(flow.invoke) ? flow.invoke : [flow.invoke];
      for (const invoke of invokeActivities) {
        const activity = this.parseInvokeActivity(invoke);
        if (activity) {
          activities.push(activity);
        }
      }
    }

    return activities;
  }

  /**
   * 解析扩展活动
   */
  private parseExtensionActivity(extActivity: any): ParsedActivity | null {
    // 处理 receiveEvent (Start 活动)
    if (extActivity['receiveEvent'] || extActivity['tibex:receiveEvent']) {
      const receiveEvent = extActivity['receiveEvent'] || extActivity['tibex:receiveEvent'];
      const receiveEventData = Array.isArray(receiveEvent) ? receiveEvent[0] : receiveEvent;

      return {
        id: receiveEventData.$?.['tibex:xpdlId'] || receiveEventData.$?.['xpdlId'] || '',
        name: receiveEventData.$.name,
        type: 'start',
        inputVariable: receiveEventData.$.variable,
        links: this.parseActivityLinks(receiveEventData)
      };
    }

    // 处理 activityExtension (End, Log 等活动)
    if (extActivity['activityExtension'] || extActivity['tibex:activityExtension']) {
      const activityExt = extActivity['activityExtension'] || extActivity['tibex:activityExtension'];
      const activityExtData = Array.isArray(activityExt) ? activityExt[0] : activityExt;

      // 解析配置信息
      let config: any = null;

      // 尝试不同的配置路径
      if (activityExtData.config) {
        const configData = Array.isArray(activityExtData.config) ? activityExtData.config[0] : activityExtData.config;
        config = configData?.['bwext:BWActivity'] || configData?.['BWActivity'];
      } else if (activityExtData['tibex:config']) {
        const configData = Array.isArray(activityExtData['tibex:config']) ? activityExtData['tibex:config'][0] : activityExtData['tibex:config'];
        config = configData?.['bwext:BWActivity'] || configData?.['BWActivity'];
      }

      // 如果 config 是数组，取第一个元素
      if (Array.isArray(config)) {
        config = config[0];
      }

      const activityTypeID = config?.$?.activityTypeID;



      let type: ParsedActivity['type'] = 'transform';
      if (activityTypeID === 'bw.internal.end') {
        type = 'end';
      } else if (activityTypeID === 'bw.generalactivities.log') {
        type = 'log';
      }

      return {
        id: activityExtData.$?.['tibex:xpdlId'] || activityExtData.$?.['xpdlId'] || '',
        name: activityExtData.$.name,
        type,
        inputVariable: activityExtData.$.inputVariable,
        expression: activityExtData.$.expression,
        expressionLanguage: activityExtData.$.expressionLanguage,
        links: this.parseActivityLinks(activityExtData),
        config: config ? {
          activityTypeID: config.$.activityTypeID,
          version: config.$.version
        } : undefined
      };
    }

    return null;
  }

  /**
   * 解析调用活动
   */
  private parseInvokeActivity(invoke: any): ParsedActivity | null {
    return {
      id: invoke.$['tibex:xpdlId'] || '',
      name: invoke.$.name,
      type: 'invoke',
      inputVariable: invoke.$.inputVariable,
      outputVariable: invoke.$.outputVariable,
      partnerLink: invoke.$.partnerLink,
      operation: invoke.$.operation,
      portType: invoke.$.portType,
      links: this.parseActivityLinks(invoke)
    };
  }

  /**
   * 解析活动链接
   */
  private parseActivityLinks(activity: any): { sources: string[]; targets: string[] } {
    const sources: string[] = [];
    const targets: string[] = [];

    // 处理 sources
    if (activity.sources) {
      const sourcesData = Array.isArray(activity.sources) ? activity.sources[0] : activity.sources;
      if (sourcesData?.source) {
        const sourceList = Array.isArray(sourcesData.source) ? sourcesData.source : [sourcesData.source];
        for (const source of sourceList) {
          sources.push(source.$.linkName);
        }
      }
    }

    // 处理 targets
    if (activity.targets) {
      const targetsData = Array.isArray(activity.targets) ? activity.targets[0] : activity.targets;
      if (targetsData?.target) {
        const targetList = Array.isArray(targetsData.target) ? targetsData.target : [targetsData.target];
        for (const target of targetList) {
          targets.push(target.$.linkName);
        }
      }
    }

    return { sources, targets };
  }

  /**
   * 提取 REST 端点信息
   */
  private extractRestEndpoints(
    partnerLinks: ParsedPartnerLink[],
    processInterface: ParsedBWPProcess['interface']
  ): ParsedRestEndpoint[] {
    const endpoints: ParsedRestEndpoint[] = [];

    for (const link of partnerLinks) {
      if (link.restBinding) {
        for (const operation of link.restBinding.operations) {
          endpoints.push({
            path: link.restBinding.path,
            method: operation.httpMethod,
            operationName: operation.name,
            inputType: processInterface.inputType,
            outputType: processInterface.outputType,
            parameters: operation.parameters
          });
        }
      }
    }

    return endpoints;
  }

  /**
   * 提取类名
   */
  private extractClassName(processName: string): string {
    const parts = processName.split('.');
    return parts[parts.length - 1];
  }
}
